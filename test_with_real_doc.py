#!/usr/bin/env python3
"""
测试使用真实文档 docs/test.docx 的字数统计
"""

import requests
import json
import time
import os

# API配置
BASE_URL = "http://localhost:8000"
LOGIN_URL = f"{BASE_URL}/api/v1/auth/login"
UPLOAD_URL = f"{BASE_URL}/api/v1/documents/upload"
TASK_STATUS_URL = f"{BASE_URL}/api/v1/tasks"

# 测试用户凭据
USERNAME = "8966097"
PASSWORD = "heibailan5112"

def login():
    """登录获取token"""
    print("🔐 正在登录...")

    # 使用form data格式
    data = {
        "username": USERNAME,
        "password": PASSWORD
    }

    response = requests.post(LOGIN_URL, data=data)

    if response.status_code == 200:
        result = response.json()
        token = result["data"]["access_token"]
        print("✅ 登录成功")
        return token
    else:
        print(f"❌ 登录失败: {response.text}")
        return None

def upload_document(token, file_path):
    """上传文档"""
    print(f"📤 正在上传文档: {file_path}")

    headers = {"Authorization": f"Bearer {token}"}

    with open(file_path, 'rb') as f:
        files = {'file': (os.path.basename(file_path), f, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
        data = {'analysis_type': 'paper_check'}

        response = requests.post(UPLOAD_URL, headers=headers, files=files, data=data)

    if response.status_code == 200:
        result = response.json()
        task_id = result["data"]["task_id"]
        print(f"✅ 文档上传成功，任务ID: {task_id}")
        return task_id
    else:
        print(f"❌ 文档上传失败: {response.text}")
        return None

def wait_for_completion(token, task_id):
    """等待任务完成"""
    print("⏳ 等待任务完成...")
    headers = {"Authorization": f"Bearer {token}"}
    
    while True:
        response = requests.get(f"{TASK_STATUS_URL}/{task_id}", headers=headers)
        if response.status_code == 200:
            data = response.json()
            status = data["data"]["status"]
            progress = data["data"]["progress"]
            
            if status == "completed":
                print("✅ 任务完成")
                return data["data"]
            elif status == "failed":
                print(f"❌ 任务失败: {data['data'].get('error_message', '未知错误')}")
                return None
            else:
                print(f"⏳ 任务进行中... ({status}) - 进度: {progress}%")
                time.sleep(3)
        else:
            print(f"❌ 获取任务状态失败: {response.text}")
            return None

def analyze_results(task_data):
    """分析结果"""
    print("🔍 分析结果...")
    
    result = task_data.get('result', {})
    document_structures = result.get('document_structures', [])
    
    print(f"📋 找到 {len(document_structures)} 个文档结构")
    
    # 保存完整结果到文件
    with open('backend/result.json', 'w', encoding='utf-8') as f:
        json.dump(task_data, f, ensure_ascii=False, indent=4)
    print("💾 完整结果已保存到 backend/result.json")
    
    # 分析每个结构
    for i, structure in enumerate(document_structures):
        name = structure.get('name', '未知')
        count = structure.get('count', 'N/A')
        page = structure.get('page', 'N/A')
        
        print(f"📊 结构 {i}: {name}")
        print(f"   页面: {page}")
        print(f"   count字段: {count}")
        
        # 特别关注参考文献
        if name == '参考文献':
            content = structure.get('content', {})
            text = content.get('text', '')
            print(f"   🔍 参考文献详细信息:")
            print(f"      内容长度: {len(text)}")
            print(f"      内容预览: {text[:200]}...")
            
            # 统计参考文献条数
            import re
            ref_count = len(re.findall(r'\[\d+\]', text))
            print(f"      检测到的参考文献条数: {ref_count}")
        
        print()

def main():
    """主函数"""
    print("🚀 测试真实文档的字数统计")
    print("=" * 50)
    
    # 检查文档是否存在
    doc_path = "docs/test.docx"
    if not os.path.exists(doc_path):
        print(f"❌ 文档不存在: {doc_path}")
        return
    
    # 登录
    token = login()
    if not token:
        return
    
    # 上传文档
    task_id = upload_document(token, doc_path)
    if not task_id:
        return
    
    # 等待完成
    task_data = wait_for_completion(token, task_id)
    if not task_data:
        return
    
    # 分析结果
    analyze_results(task_data)
    
    print(f"🌐 前端页面: http://localhost:3000/document/{task_id}")

if __name__ == "__main__":
    main()
