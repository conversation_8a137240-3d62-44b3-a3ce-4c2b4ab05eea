#!/usr/bin/env python3
"""
检查API响应中的参考文献数据
"""

import requests
import json

# API配置
API_BASE_URL = "http://localhost:8000"
TEST_USERNAME = "8966097"
TEST_PASSWORD = "heibailan5112"
TASK_ID = "task_999e6afa5b5f4a6b8084723459c2ce49"

def check_api_response():
    """检查API响应"""
    session = requests.Session()
    
    # 1. 登录
    print("🔐 正在登录...")
    login_data = {
        'username': TEST_USERNAME,
        'password': TEST_PASSWORD
    }
    
    response = session.post(f"{API_BASE_URL}/api/v1/auth/login", data=login_data)
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code}")
        return
    
    result = response.json()
    if not result.get('success'):
        print(f"❌ 登录失败: {result.get('message')}")
        return
    
    access_token = result['data']['access_token']
    print(f"✅ 登录成功")
    
    # 2. 获取任务数据
    print(f"📊 获取任务数据: {TASK_ID}")
    headers = {'Authorization': f'Bearer {access_token}'}
    
    response = session.get(f"{API_BASE_URL}/api/v1/tasks/{TASK_ID}", headers=headers)
    if response.status_code != 200:
        print(f"❌ 获取任务失败: {response.status_code}")
        return
    
    result = response.json()
    if not result.get('success'):
        print(f"❌ 获取任务失败: {result.get('message')}")
        return
    
    # 3. 检查参考文献数据
    task_data = result['data']
    document_structures = task_data.get('result', {}).get('document_structures', [])
    
    print(f"📋 找到 {len(document_structures)} 个文档结构")
    
    for i, structure in enumerate(document_structures):
        name = structure.get('name', '')
        if '参考文献' in name:
            print(f"\n🔍 找到参考文献结构 (索引 {i}):")
            print(f"   名称: {name}")
            print(f"   页面: {structure.get('page')}")
            print(f"   状态: {structure.get('status')}")
            print(f"   字数: {structure.get('word_count')}")
            
            # 检查参考文献相关字段
            ref_fields = ['reference_count', 'reference_chinese_count', 'reference_foreign_count', 'reference_display']
            for field in ref_fields:
                value = structure.get(field)
                print(f"   {field}: {value}")
            
            # 检查内容
            content = structure.get('content', {})
            text = content.get('text', '')
            print(f"   内容长度: {len(text)}")
            print(f"   内容预览: {text[:200]}...")
            
            break
    else:
        print("❌ 未找到参考文献结构")
        
        # 显示所有结构名称
        print("\n📋 所有结构名称:")
        for i, structure in enumerate(document_structures):
            name = structure.get('name', '')
            print(f"   {i}: {name}")

if __name__ == "__main__":
    check_api_response()
