#!/usr/bin/env python3
"""
测试参考文献语言分类功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.services.document_processor import DocumentProcessor

def test_reference_language_classification():
    """测试参考文献语言分类功能"""
    
    # 创建DocumentProcessor实例
    processor = DocumentProcessor()
    
    # 测试文本（从实际测试结果中获取）
    test_text = """[1]高绿苑."沉浸式"舞蹈剧场中观演关系研究[D].山东师范大学,2023.
[2]廖伟.舞台表演视角下新媒体舞蹈的发展策略研究[D].华侨大学,2023.
[3]郭佩祎,毕思文.论新媒体技术对当代舞蹈创作的影响[J].舞蹈,2022(06):45-48.
[4]<PERSON>, J. Dance and Technology: A New Era[J]. Dance Studies, 2023, 15(2): 123-135.
[5]<PERSON>, <PERSON>. Virtual Reality in Performance Arts[M]. New York: Academic Press, 2022.
[6]李明.现代舞蹈编导技法研究[M].北京:人民音乐出版社,2021.
[7]Brown, A. Digital Choreography: The Future of Dance[J]. Contemporary Dance Review, 2023, 8(1): 67-82.
[8]王芳.新媒体艺术与舞蹈融合发展研究[D].北京舞蹈学院,2022.
[9]Davis, R. & Wilson, K. Interactive Dance Performances[C]. Proceedings of Digital Arts Conference, 2023: 45-52.
[10]张华.舞蹈创作中的科技元素运用[J].舞蹈艺术,2023(03):78-82.
[11]Miller, S. Augmented Reality in Dance Education[J]. Dance Technology Quarterly, 2022, 4(3): 12-18.
[12]刘洋.虚拟现实技术在舞蹈教学中的应用[J].艺术教育,2023(02):156-159.
[13]Thompson, L. Motion Capture Technology for Dance Analysis[M]. London: Arts & Technology Press, 2023.
[14]陈静.数字化舞蹈创作的美学思考[J].艺术研究,2022(04):89-93."""
    
    print("🧪 测试参考文献语言分类功能")
    print("=" * 50)
    print(f"📝 测试文本:\n{test_text}")
    print("=" * 50)
    
    # 调用语言分类方法
    chinese_count, foreign_count = processor._count_references_by_language(test_text)
    
    print(f"📊 分类结果:")
    print(f"   中文参考文献: {chinese_count}条")
    print(f"   外文参考文献: {foreign_count}条")
    print(f"   总计: {chinese_count + foreign_count}条")
    
    # 验证结果
    expected_chinese = 8  # [1], [2], [3], [6], [8], [10], [12], [14]
    expected_foreign = 6  # [4], [5], [7], [9], [11], [13]
    
    print("=" * 50)
    print(f"🎯 预期结果:")
    print(f"   中文参考文献: {expected_chinese}条")
    print(f"   外文参考文献: {expected_foreign}条")
    print(f"   总计: {expected_chinese + expected_foreign}条")
    
    print("=" * 50)
    if chinese_count == expected_chinese and foreign_count == expected_foreign:
        print("✅ 测试通过！语言分类功能正常工作")
        return True
    else:
        print("❌ 测试失败！语言分类结果不符合预期")
        return False

if __name__ == "__main__":
    test_reference_language_classification()
