{"success": true, "code": 200, "message": "获取任务信息成功", "data": {"filename": "test.docx", "file_size": 2560238, "analysis_options": {}, "user_id": "user_2450b8b44a9c4db0842e36cd9e99ed65", "task_id": "task_ed93e3a060d24bc2b4a3a12ff4eff894", "file_path": "D:\\Works\\paper-check-win\\backend\\data\\uploads\\user_2450b8b44a9c4db0842e36cd9e99ed65\\task_ed93e3a060d24bc2b4a3a12ff4eff894_test.docx", "task_type": "paper_check", "status": "completed", "progress": 100, "created_at": "2025-07-29T15:43:59.997571+00:00", "started_at": null, "completed_at": "2025-07-29T15:45:36.573924+00:00", "processing_time": null, "error_message": null, "result": {"success": true, "processing_time": 96.54034113883972, "error_message": null, "warnings": [], "document_info": {"title": "新媒体技术对舞蹈编导创作手法的影响研究", "author": "李岩", "major": "舞蹈编导（专升本）", "department": "艺术学院", "student_id": "32219350130", "advisor": "展烨", "date": "2025年5月20日", "degree_type": "学士学位论文", "school": "艺术学院"}, "content_stats": {"page_size": "595x842", "font_count": 5, "fonts_used": ["Times New Roman", "黑体", "<PERSON><PERSON>", "仿宋", "宋体"], "line_count": 1022, "page_count": 36, "word_count": 18806, "chart_count": 0, "field_count": 82, "image_count": 9, "margin_info": {"top": 70.9000015258789, "left": 56.70000076293945, "right": 56.70000076293945, "bottom": 56.70000076293945}, "style_count": 23, "styles_used": [{"name": "正文", "count": 243}, {"name": "表格文本", "count": 101}, {"name": "任务书-表格-居中文本", "count": 57}, {"name": "开题报告-表格-主要内容", "count": 57}, {"name": "参考文献正文-自动列表", "count": 42}, {"name": "任务书-表格-居中文本-大行距", "count": 30}, {"name": "TOC 2", "count": 21}, {"name": "标题 2", "count": 21}, {"name": "任务书-表格-主要内容", "count": 18}, {"name": "开题报告-表格-居中文本", "count": 17}], "table_count": 10, "comment_count": 0, "drawing_count": 0, "endnote_count": 0, "formula_count": 0, "heading_count": 49, "section_count": 22, "textbox_count": 0, "version_count": 0, "bookmark_count": 0, "equation_count": 0, "footnote_count": 2, "grammar_errors": 0, "revision_count": 0, "character_count": 22630, "hyperlink_count": 36, "paragraph_count": 514, "reference_count": 14, "spelling_errors": 64, "page_orientation": "portrait", "line_spacing_info": {"12.0": 292, "15.0": 99, "18.0": 63, "20.0": 159, "27.0": 15}, "track_changes_count": 0, "characters_with_spaces": 23878}, "document_structures": [{"name": "封面", "page": 1, "type": "standard", "status": "present", "content": {"text": "学士学位论文", "style": "正文", "paragraph_index": 1}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "任务书", "page": 2, "type": "standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计（论文）任务书", "style": "任务书-标题", "paragraph_index": 0}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "开题报告", "page": 3, "type": "standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计(论文)开题报告", "style": "开题报告-标题", "paragraph_index": 0}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "河北科技学院本科生毕业设计（论文）测试", "page": 7, "type": "non_standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计（论文）测试", "style": "任务书-标题", "paragraph_index": 0}, "required": false, "word_count": 0, "reference_count": 0}, {"name": "诚信声明", "page": 9, "type": "standard", "status": "present", "content": {"text": "学位论文原创性声明", "style": "标题 1- 非目录", "paragraph_index": 0}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "版权声明", "page": 9, "type": "standard", "status": "present", "content": {"text": "学位论文版权使用授权书", "style": "标题 1- 非目录", "paragraph_index": 5}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "河北科技学院本科生毕业设计（论文）测试2", "page": 10, "type": "non_standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计（论文）测试2", "style": "任务书-标题", "paragraph_index": 0}, "required": false, "word_count": 0, "reference_count": 0}, {"name": "中文摘要", "page": 11, "type": "standard", "status": "present", "content": {"text": "摘　　要", "style": "标题 1- 非目录", "paragraph_index": 0}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "中文关键词", "page": 11, "type": "standard", "status": "present", "content": {"text": "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法", "style": "正文", "paragraph_index": 2}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "英文摘要", "page": 12, "type": "standard", "status": "present", "content": {"text": "ABSTRACT", "style": "标题 1- 非目录", "paragraph_index": 0}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "英文关键词", "page": 12, "type": "standard", "status": "present", "content": {"text": "Key words:dance creation;virtual reality (VR); augmented reality (AR); short video communication; ch", "style": "正文", "paragraph_index": 2}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "目录", "page": 13, "type": "standard", "status": "present", "content": {"text": "目　　录", "style": "标题 1- 非目录", "paragraph_index": 0}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "正文", "page": 15, "type": "standard", "status": "present", "content": {"text": "绪论", "style": "标题 1", "paragraph_index": 0}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "结　　论", "page": 32, "type": "non_standard", "status": "present", "content": {"text": "结　　论", "style": "标题1 - 目录 - 非编号", "paragraph_index": 0}, "required": false, "word_count": 0, "reference_count": 0}, {"name": "参考文献", "page": 5, "type": "standard", "status": "present", "content": {"text": "[1]高绿苑.“沉浸式”舞蹈剧场中观演关系研究[D].山东师范大学,2023.\n[2]廖伟.舞台表演视角下新媒体舞蹈的发展策略研究[D].华侨大学,2023.\n[3]郭佩祎,毕思文.论新媒体技术对当代", "style": "标题1 - 目录 - 非编号", "paragraph_index": 0}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "列表啊", "page": 34, "type": "non_standard", "status": "present", "content": {"text": "列表啊", "style": "标题1 - 目录 - 非编号", "paragraph_index": 0}, "required": false, "word_count": 0, "reference_count": 0}, {"name": "致谢", "page": 35, "type": "standard", "status": "present", "content": {"text": "致　　谢", "style": "标题1 - 目录 - 非编号", "paragraph_index": 0}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "列表啊2", "page": 36, "type": "non_standard", "status": "present", "content": {"text": "列表啊2", "style": "标题1 - 目录 - 非编号", "paragraph_index": 0}, "required": false, "word_count": 0, "reference_count": 0}, {"name": "附录", "page": null, "type": "standard", "status": "missing", "required": true, "word_count": 0, "reference_count": 0}], "detection_standard": null, "standard_name": "论文检测", "task_type": "paper_check", "status": "completed", "compliance_score": 85, "problems_found": 0, "check_summary": {"major_problems": 0, "minor_problems": 0, "total_problems": 0, "compliance_score": 85}, "analysis_summary": {"document_type": "report", "quality_score": 0, "hierarchy_score": 0, "completeness_score": 0}, "processing_meta": {"processing_times": {"preprocessing": 0.004065036773681641, "raw_extraction": 95.68038630485535, "structure_analysis": 0.003999471664428711}, "extraction_method": "word_com_pool", "processing_pipeline": "optimized_v2"}}, "updated_at": "2025-07-29T15:45:36.577237+00:00", "current_step": "step_4", "steps": {"step_1": {"step_id": "step_1", "name": "文档验证", "description": "文档验证完成", "progress": 100, "status": "completed", "start_time": "2025-07-29T15:44:00.012570", "end_time": "2025-07-29T15:44:00.516305", "error_message": null}, "step_2": {"step_id": "step_2", "name": "原始数据提取", "description": "数据提取成功", "progress": 100, "status": "completed", "start_time": "2025-07-29T15:44:00.516587", "end_time": "2025-07-29T15:45:36.231044", "error_message": null}, "step_3": {"step_id": "step_3", "name": "数据预处理", "description": "数据预处理成功", "progress": 100, "status": "completed", "start_time": "2025-07-29T15:45:36.231044", "end_time": "2025-07-29T15:45:36.236110", "error_message": null}, "step_4": {"step_id": "step_4", "name": "结构分析", "description": "分析报告已生成", "progress": 100, "status": "completed", "start_time": "2025-07-29T15:45:36.236110", "end_time": "2025-07-29T15:45:36.551907", "error_message": null}, "step_5": {"step_id": "step_5", "name": "格式规则检测", "description": "格式检测完成", "progress": 100, "status": "completed", "start_time": "2025-07-29T15:45:36.240109", "end_time": "2025-07-29T15:45:36.257180", "error_message": null}, "step_6": {"step_id": "step_6", "name": "生成分析报告", "description": "正在生成分析报告", "progress": 100, "status": "completed", "start_time": "2025-07-29T15:45:36.258181", "end_time": "2025-07-29T15:45:36.570923", "error_message": null}}, "estimated_completion": "2025-07-29T15:45:36.551907", "live_progress": true}, "timestamp": 1753804328, "request_id": null}