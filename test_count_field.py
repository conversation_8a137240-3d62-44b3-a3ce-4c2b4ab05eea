#!/usr/bin/env python3
"""
测试新的count字段
"""

import requests
import json
import time
import os

# API配置
API_BASE_URL = "http://localhost:8000"
TEST_USERNAME = "8966097"
TEST_PASSWORD = "heibailan5112"
TEST_DOCUMENT = "docs/test.docx"

def test_count_field():
    """测试count字段"""
    print("🚀 测试新的count字段")
    print("=" * 50)
    
    session = requests.Session()
    
    # 1. 登录
    print("🔐 正在登录...")
    login_data = {
        'username': TEST_USERNAME,
        'password': TEST_PASSWORD
    }
    
    response = session.post(f"{API_BASE_URL}/api/v1/auth/login", data=login_data)
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code}")
        return
    
    result = response.json()
    if not result.get('success'):
        print(f"❌ 登录失败: {result.get('message')}")
        return
    
    access_token = result['data']['access_token']
    print(f"✅ 登录成功")
    
    # 2. 上传文档
    print("📤 正在上传测试文档...")
    headers = {'Authorization': f'Bearer {access_token}'}
    
    with open(TEST_DOCUMENT, 'rb') as f:
        files = {'file': (os.path.basename(TEST_DOCUMENT), f, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
        data = {'analysis_type': 'paper_check'}
        
        response = session.post(f"{API_BASE_URL}/api/v1/documents/upload", headers=headers, files=files, data=data)
    
    if response.status_code != 200:
        print(f"❌ 上传失败: {response.status_code}")
        return
    
    result = response.json()
    if not result.get('success'):
        print(f"❌ 上传失败: {result.get('message')}")
        return
    
    task_id = result['data']['task_id']
    print(f"✅ 文档上传成功，任务ID: {task_id}")
    
    # 3. 等待任务完成
    print("⏳ 等待任务完成...")
    max_wait_time = 300
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        response = session.get(f"{API_BASE_URL}/api/v1/tasks/{task_id}", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_data = result['data']
                status = task_data.get('status')
                
                if status == 'completed':
                    print("✅ 任务完成")
                    break
                elif status == 'failed':
                    print(f"❌ 任务失败: {task_data.get('error_message')}")
                    return
                else:
                    print(f"⏳ 任务进行中... ({status})")
                    time.sleep(5)
                    continue
        
        time.sleep(5)
    else:
        print("⏰ 等待超时")
        return
    
    # 4. 检查count字段
    print("🔍 检查count字段...")
    document_structures = task_data.get('result', {}).get('document_structures', [])
    
    print(f"📋 找到 {len(document_structures)} 个文档结构")
    
    for i, structure in enumerate(document_structures):
        name = structure.get('name', '')
        count = structure.get('count')
        word_count = structure.get('word_count')
        
        print(f"📊 结构 {i}: {name}")
        print(f"   count字段: {count}")
        print(f"   word_count字段: {word_count}")
        
        if name == '参考文献':
            print(f"   🔍 参考文献详细信息:")
            print(f"      reference_chinese_count: {structure.get('reference_chinese_count')}")
            print(f"      reference_foreign_count: {structure.get('reference_foreign_count')}")
            print(f"      内容长度: {len(structure.get('content', {}).get('text', ''))}")
        
        print()
    
    print(f"🌐 前端页面: http://localhost:3000/statistics-report/{task_id}")

if __name__ == "__main__":
    test_count_field()
