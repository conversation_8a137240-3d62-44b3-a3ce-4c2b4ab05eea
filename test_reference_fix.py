#!/usr/bin/env python3
"""
测试参考文献中文/外文统计修复
"""

import requests
import json
import time
import os
from pathlib import Path

# API配置
API_BASE_URL = "http://localhost:8000"
TEST_USERNAME = "8966097"
TEST_PASSWORD = "heibailan5112"
TEST_DOCUMENT = "docs/test.docx"

class PaperCheckTester:
    def __init__(self):
        self.session = requests.Session()
        self.access_token = None
        
    def login(self):
        """登录获取访问令牌"""
        print("🔐 正在登录...")
        
        # 使用FormData格式登录
        login_data = {
            'username': TEST_USERNAME,
            'password': TEST_PASSWORD
        }
        
        response = self.session.post(
            f"{API_BASE_URL}/api/v1/auth/login",
            data=login_data
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                self.access_token = result['data']['access_token']
                print(f"✅ 登录成功，用户: {result['data']['user']['username']}")
                return True
            else:
                print(f"❌ 登录失败: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    
    def upload_document(self):
        """上传测试文档"""
        print("📤 正在上传测试文档...")
        
        if not os.path.exists(TEST_DOCUMENT):
            print(f"❌ 测试文档不存在: {TEST_DOCUMENT}")
            return None
            
        headers = {
            'Authorization': f'Bearer {self.access_token}'
        }
        
        with open(TEST_DOCUMENT, 'rb') as f:
            files = {'file': (os.path.basename(TEST_DOCUMENT), f, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            data = {'analysis_type': 'paper_check'}
            
            response = self.session.post(
                f"{API_BASE_URL}/api/v1/documents/upload",
                headers=headers,
                files=files,
                data=data
            )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result['data']['task_id']
                print(f"✅ 文档上传成功，任务ID: {task_id}")
                return task_id
            else:
                print(f"❌ 文档上传失败: {result.get('message', '未知错误')}")
                return None
        else:
            print(f"❌ 上传请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
    
    def wait_for_task_completion(self, task_id, max_wait_time=300):
        """等待任务完成"""
        print(f"⏳ 等待任务完成: {task_id}")
        
        headers = {
            'Authorization': f'Bearer {self.access_token}'
        }
        
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            response = self.session.get(
                f"{API_BASE_URL}/api/v1/tasks/{task_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    task_data = result['data']
                    status = task_data.get('status')
                    print(f"📊 任务状态: {status}")
                    
                    if status == 'completed':
                        print("✅ 任务完成")
                        return task_data
                    elif status == 'failed':
                        print(f"❌ 任务失败: {task_data.get('error_message', '未知错误')}")
                        return None
                    elif status in ['pending', 'processing']:
                        print(f"⏳ 任务进行中... ({status})")
                        time.sleep(5)
                        continue
                    else:
                        print(f"⚠️ 未知任务状态: {status}")
                        time.sleep(5)
                        continue
                else:
                    print(f"❌ 获取任务状态失败: {result.get('message', '未知错误')}")
                    return None
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return None
        
        print("⏰ 等待超时")
        return None
    
    def check_reference_statistics(self, task_data):
        """检查参考文献统计信息"""
        print("🔍 检查参考文献统计信息...")

        # 打印完整的任务数据以便调试
        print(f"📋 完整任务数据: {json.dumps(task_data, indent=2, ensure_ascii=False)}")

        # 从任务数据中提取document_structures
        document_structures = task_data.get('document_structures', [])

        if not document_structures:
            print("❌ 未找到document_structures数据")
            # 尝试从result中获取数据
            result_data = task_data.get('result')
            if result_data:
                print("🔍 尝试从result中获取document_structures...")
                if isinstance(result_data, str):
                    try:
                        result_data = json.loads(result_data)
                    except:
                        pass
                if isinstance(result_data, dict):
                    document_structures = result_data.get('document_structures', [])
                    if document_structures:
                        print("✅ 从result中找到document_structures")

            if not document_structures:
                return False
        
        # 查找参考文献结构
        reference_structure = None
        for structure in document_structures:
            if structure.get('structure_name') == '参考文献' or structure.get('name') == '参考文献':
                reference_structure = structure
                break
        
        if not reference_structure:
            print("❌ 未找到参考文献结构")
            return False
        
        print("✅ 找到参考文献结构")
        print(f"📊 参考文献结构数据: {json.dumps(reference_structure, indent=2, ensure_ascii=False)}")
        
        # 检查是否有详细的中文/外文统计
        chinese_count = reference_structure.get('reference_chinese_count')
        foreign_count = reference_structure.get('reference_foreign_count')
        reference_display = reference_structure.get('reference_display')
        total_count = reference_structure.get('reference_count')
        
        print(f"📈 参考文献统计:")
        print(f"   总数: {total_count}")
        print(f"   中文: {chinese_count}")
        print(f"   外文: {foreign_count}")
        print(f"   显示格式: {reference_display}")
        
        # 验证修复是否成功
        if chinese_count is not None and foreign_count is not None:
            if chinese_count > 0 and foreign_count > 0:
                expected_display = f"中文{chinese_count}条;外文{foreign_count}条"
                print(f"✅ 修复成功！参考文献详细统计: {expected_display}")
                return True
            elif chinese_count > 0:
                expected_display = f"中文{chinese_count}条"
                print(f"✅ 修复成功！参考文献统计: {expected_display}")
                return True
            elif foreign_count > 0:
                expected_display = f"外文{foreign_count}条"
                print(f"✅ 修复成功！参考文献统计: {expected_display}")
                return True
            else:
                print("⚠️ 参考文献统计为0，可能文档中没有参考文献")
                return False
        else:
            print("❌ 修复失败！未找到中文/外文统计字段")
            return False
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 开始测试参考文献中文/外文统计修复")
        print("=" * 50)
        
        # 1. 登录
        if not self.login():
            return False
        
        # 2. 上传文档
        task_id = self.upload_document()
        if not task_id:
            return False
        
        # 3. 等待任务完成
        task_data = self.wait_for_task_completion(task_id)
        if not task_data:
            return False
        
        # 4. 检查参考文献统计
        success = self.check_reference_statistics(task_data)
        
        print("=" * 50)
        if success:
            print("🎉 测试成功！参考文献中文/外文统计修复有效")
        else:
            print("💥 测试失败！参考文献中文/外文统计修复无效")
        
        return success

if __name__ == "__main__":
    tester = PaperCheckTester()
    tester.run_test()
